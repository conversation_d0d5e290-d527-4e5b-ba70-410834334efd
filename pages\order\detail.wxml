<!--pages/order/detail.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">加载中...</view>
  </view>

  <!-- 订单详情 -->
  <block wx:if="{{!loading && order}}">
    <!-- 订单状态 -->
    <view class="status-card">
      <view class="status-icon status-{{order.status}}"></view>
      <view class="status-text">{{order.statusText}}</view>
      <view class="status-desc">
        <block wx:if="{{order.status === 1}}">
          我们已收到您的回收申请，正在安排专业人员处理
        </block>
        <block wx:elif="{{order.status === 2}}">
          专业人员正在鉴定您的黄金，请耐心等待
        </block>
        <block wx:elif="{{order.status === 3}}">
          专业人员正在检测您的黄金，请耐心等待
        </block>
        <block wx:elif="{{order.status === 4}}">
          检测已完成，请确认最终价格
        </block>
        <block wx:elif="{{order.status === 5}}">
          您已确认最终价格，订单处理中
        </block>
        <block wx:elif="{{order.status === 6}}">
          订单已完成，感谢您的信任
        </block>
        <block wx:elif="{{order.status === 0}}">
          订单已取消：{{order.cancelReason || '用户取消'}}
        </block>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-card">
      <view class="card-title">收货信息</view>
      <view class="address-content">
        <view class="address-loading" wx:if="{{loadingAddress}}">
          <text class="loading-text">加载地址信息中...</text>
        </view>
        <block wx:elif="{{addressInfo}}">
          <view class="address-header">
            <view class="receiver-info">
              <text class="receiver-name">{{addressInfo.receiverName}}</text>
              <text class="receiver-phone">{{addressInfo.receiverPhone}}</text>
            </view>
            <view class="default-tag" wx:if="{{addressInfo.isDefault === 1}}">默认</view>
          </view>
          <view class="address-detail">{{addressInfo.fullAddress}}</view>
          <view class="address-extra" wx:if="{{addressInfo.postCode}}">
            <text class="postcode">邮编：{{addressInfo.postCode}}</text>
          </view>
        </block>
        <view class="address-fallback" wx:else>
          <view class="receiver-info">
            <text class="receiver-name">{{order.receiverName}}</text>
            <text class="receiver-phone">{{order.receiverPhone}}</text>
          </view>
          <view class="address-detail">{{order.receiverAddress || '地址信息获取中...'}}</view>
        </view>
      </view>
    </view>

    <!-- 物流信息 (仅在待取件状态显示) -->
    <view class="logistics-card" wx:if="{{order.status === 2}}">
      <view class="card-title">物流信息</view>

      <!-- 加载中 -->
      <view class="logistics-loading" wx:if="{{loadingExpress}}">
        <view class="loading-text">正在获取物流信息...</view>
      </view>

      <!-- 物流信息内容 -->
      <view class="logistics-content" wx:elif="{{expressInfo && !expressInfo.error}}">
        <view class="logistics-header">
          <view class="logistics-company">{{expressInfo.company}}</view>
          <view class="logistics-status">{{expressInfo.status}}</view>
        </view>

        <!-- 快递单号信息 -->
        <view class="logistics-number" wx:if="{{pickupInfo.expressNumber}}">
          <text>运单号：{{pickupInfo.expressNumber}}</text>
          <text class="copy-btn" bindtap="copyExpressNumber">复制</text>
        </view>

        <!-- 快递员信息 -->
        <view class="courier-info" wx:if="{{pickupInfo.courierName || pickupInfo.courierPhone}}">
          <view class="courier-item" wx:if="{{pickupInfo.courierName}}">
            <text>快递员：{{pickupInfo.courierName}}</text>
          </view>
          <view class="courier-item" wx:if="{{pickupInfo.courierPhone}}" bindtap="callPickupCourier">
            <text>联系电话：{{pickupInfo.courierPhone}}</text>
            <text class="call-btn">拨打</text>
          </view>
        </view>

        <!-- 时间信息 -->
        <view class="time-info">
          <view class="time-item" wx:if="{{pickupInfo.pickupTime}}">
            <text>取件时间：{{pickupInfo.pickupTime}}</text>
          </view>
          <view class="time-item" wx:if="{{pickupInfo.estimatedDelivery}}">
            <text>预计送达：{{pickupInfo.estimatedDelivery}}</text>
          </view>
          <view class="time-item" wx:if="{{expressInfo.takeTime}}">
            <text>运输时长：{{expressInfo.takeTime}}</text>
          </view>
        </view>

        <!-- 物流轨迹 -->
        <view class="logistics-tracks" wx:if="{{expressInfo.hasTracking}}">
          <view class="tracks-title">
            物流轨迹
            <text class="tracks-count" wx:if="{{expressInfo.tracksCount}}">(共{{expressInfo.tracksCount}}条)</text>
          </view>
          <view class="track-list">
            <view class="track-item" wx:for="{{expressInfo.tracks}}" wx:key="index">
              <view class="track-time">{{item.time}}</view>
              <view class="track-status">{{item.status}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 物流信息错误 -->
      <view class="logistics-error" wx:elif="{{expressInfo && expressInfo.error}}">
        <view class="error-text">{{expressInfo.message}}</view>
        <view class="retry-btn" bindtap="retryLoadExpress">重新获取</view>
      </view>

      <!-- 无物流信息 -->
      <view class="logistics-empty" wx:else>
        <view class="empty-text">暂无物流信息</view>
      </view>
    </view>

    <!-- 黄金信息 -->
    <view class="gold-card">
      <view class="card-title">黄金信息</view>
      <view class="gold-content">
        <view class="gold-image-section" wx:if="{{order.hasImage}}">
          <view class="image-gallery">
            <view
              class="image-item"
              wx:for="{{order.imageList}}"
              wx:key="*this"
              bindtap="previewImage"
              data-index="{{index}}"
            >
              <image
                class="gold-image"
                src="{{item}}"
                mode="aspectFill"
                lazy-load="{{true}}"
                binderror="onImageError"
                bindload="onImageLoad"
              ></image>
              <view class="image-index">{{index + 1}}/{{order.imageCount}}</view>
            </view>
          </view>
          <view class="image-tip">点击图片查看大图 (共{{order.imageCount}}张)</view>
        </view>
        <view class="no-image-section" wx:else>
          <view class="no-image-placeholder">
            <text class="no-image-text">暂无图片</text>
          </view>
        </view>
        <view class="gold-details">
          <view class="detail-item">
            <text class="detail-label">黄金类型:</text>
            <text class="detail-value">{{order.goldTypeText}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">纯度:</text>
            <text class="detail-value">{{order.purity}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">品相:</text>
            <text class="detail-value">{{order.goldCondition}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">预估重量:</text>
            <text class="detail-value">{{order.estimatedWeight}}g</text>
          </view>
          <view class="detail-item" wx:if="{{order.description}}">
            <text class="detail-label">描述:</text>
            <text class="detail-value">{{order.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 价格信息 -->
    <view class="price-card" wx:if="{{order.goldType === 'jewelry'}}">
      <view class="card-title">价格信息</view>
      <view class="price-details">
        <view class="price-item">
          <text class="price-label">预估价格:</text>
          <text class="price-value">¥{{order.estimatedPrice}}</text>
        </view>
        <view class="price-item" wx:if="{{order.finalPrice && order.finalPrice !== '0.00'}}">
          <text class="price-label">最终价格:</text>
          <text class="price-value final-price">¥{{order.finalPrice}}</text>
        </view>
        <view class="price-item" wx:if="{{order.inspectionResult}}">
          <text class="price-label">检测结果:</text>
          <text class="price-value">{{order.inspectionResult}}</text>
        </view>
      </view>
    </view>

    <!-- 检测结果信息 (status=4,5,6时显示) -->
    <view class="inspection-card" wx:if="{{order.status === 4 || order.status === 5 || order.status === 6}}">
      <view class="card-title">检测结果</view>

      <!-- 加载中 -->
      <view class="inspection-loading" wx:if="{{loadingQuotation}}">
        <view class="loading-text">正在获取检测结果...</view>
      </view>

      <!-- 详细检测结果 -->
      <view class="inspection-details" wx:elif="{{quotationInfo && quotationInfo.hasQuotation}}">
        <!-- 检测结果项列表 -->
        <view class="quotation-items-container">
          <block wx:for="{{quotationInfo.quotations}}" wx:key="id" wx:for-item="quotation" wx:for-index="quotationIndex">
            <view class="quotation-item" wx:for="{{quotation.items}}" wx:key="id" wx:for-item="item" wx:for-index="itemIndex">
              <!-- 黄金类型标签 -->
              <view class="item-type-tag">{{item.goldTypeText}}</view>

              <!-- 左右布局：图片 + 检测数据 -->
              <view class="item-content">
                <!-- 左侧：检测图片 -->
                <view class="item-image-container">
                  <view class="item-image" wx:if="{{item.imageUrl}}">
                    <image
                      class="quotation-image"
                      src="{{item.imageUrl}}"
                      mode="aspectFill"
                      bindtap="previewQuotationImage"
                      data-url="{{item.imageUrl}}"
                      data-quotation-index="{{quotationIndex}}"
                      data-item-index="{{itemIndex}}"
                    ></image>
                  </view>
                  <view class="no-image-placeholder" wx:else>
                    <text class="no-image-text">暂无图片</text>
                  </view>
                </view>

                <!-- 右侧：检测数据 -->
                <view class="item-data">
                  <view class="data-row">
                    <view class="data-item">
                      <text class="data-label">单价</text>
                      <text class="data-value">¥{{item.price}}/g</text>
                    </view>
                    <view class="data-item">
                      <text class="data-label">含金量</text>
                      <text class="data-value">{{item.goldContent}}</text>
                    </view>
                  </view>
                  <view class="data-row">
                    <view class="data-item">
                      <text class="data-label">克重</text>
                      <text class="data-value">{{item.weight}}g</text>
                    </view>
                    <view class="data-item">
                      <text class="data-label">熔后重</text>
                      <text class="data-value">{{item.meltedWeight}}g</text>
                    </view>
                  </view>
                  <view class="data-row">
                    <view class="data-item">
                      <text class="data-label">纯金重</text>
                      <text class="data-value">{{item.pureGoldWeight}}g</text>
                    </view>
                    <view class="data-item item-total">
                      <text class="data-label">小计</text>
                      <text class="data-value total-price">¥{{item.itemTotal}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </view>

        <!-- 费用明细 -->
        <view class="fee-details">
          <view class="fee-title">费用明细</view>
          <view class="fee-list">
            <view class="fee-item">
              <text class="fee-label">检测总价</text>
              <text class="fee-value">¥{{quotationInfo.totalAmount}}</text>
            </view>
            <view class="fee-item" wx:if="{{quotationInfo.totalShippingFee !== '0.00'}}">
              <text class="fee-label">运费</text>
              <text class="fee-value">-¥{{quotationInfo.totalShippingFee}}</text>
            </view>
            <view class="fee-item" wx:if="{{quotationInfo.totalInsuranceFee !== '0.00'}}">
              <text class="fee-label">保费</text>
              <text class="fee-value">-¥{{quotationInfo.totalInsuranceFee}}</text>
            </view>
            <view class="fee-item" wx:if="{{quotationInfo.totalServiceFee !== '0.00'}}">
              <text class="fee-label">服务费</text>
              <text class="fee-value">-¥{{quotationInfo.totalServiceFee}}</text>
            </view>
            <view class="fee-item final-amount">
              <text class="fee-label">最终金额</text>
              <text class="fee-value final-price">¥{{quotationInfo.finalAmount}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮仅在status=4时显示 -->
        <view class="inspection-actions" wx:if="{{order.status === 4}}">
          <view class="action-btn confirm-btn" bindtap="confirmFinalPrice">确认价格</view>
          <view class="action-btn reject-btn" bindtap="rejectFinalPrice">有异议</view>
        </view>

        <!-- status=5时显示已确认状态 -->
        <view class="inspection-status" wx:if="{{order.status === 5}}">
          <view class="status-badge confirmed">已确认价格</view>
        </view>

        <!-- status=6时显示完成状态 -->
        <view class="inspection-status" wx:if="{{order.status === 6}}">
          <view class="status-badge completed">订单已完成</view>
        </view>
      </view>

      <!-- 简化显示（当没有详细检测结果时） -->
      <view class="inspection-details" wx:else>
        <view class="inspection-item" wx:if="{{order.finalPrice && order.finalPrice !== '0.00'}}">
          <text class="inspection-label">最终价格:</text>
          <text class="inspection-value final-price">¥{{order.finalPrice}}</text>
        </view>
        <view class="inspection-item" wx:if="{{order.inspectionResult}}">
          <text class="inspection-label">检测说明:</text>
          <text class="inspection-value">{{order.inspectionResult}}</text>
        </view>

        <!-- 操作按钮仅在status=4时显示 -->
        <view class="inspection-actions" wx:if="{{order.status === 4}}">
          <view class="action-btn confirm-btn" bindtap="confirmFinalPrice">确认价格</view>
          <view class="action-btn reject-btn" bindtap="rejectFinalPrice">有异议</view>
        </view>

        <!-- status=5时显示已确认状态 -->
        <view class="inspection-status" wx:if="{{order.status === 5}}">
          <view class="status-badge confirmed">已确认价格</view>
        </view>

        <!-- status=6时显示完成状态 -->
        <view class="inspection-status" wx:if="{{order.status === 6}}">
          <view class="status-badge completed">订单已完成</view>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-card">
      <view class="card-title">订单信息</view>
      <view class="order-info-list">
        <view class="order-info-item">
          <text class="info-label">订单编号</text>
          <view class="info-value">
            <text>{{order.orderId}}</text>
          </view>
        </view>
        <view class="order-info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{order.createTimeFormatted}}</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">更新时间</text>
          <text class="info-value">{{order.updateTimeFormatted}}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <!-- 待处理状态 -->
      <block wx:if="{{order.status === 1}}">
        <view class="bottom-action cancel" bindtap="showCancel">取消订单</view>
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 待取件状态 -->
      <block wx:if="{{order.status === 2}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 待检测状态 -->
      <block wx:if="{{order.status === 3}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 检测完成状态 -->
      <block wx:if="{{order.status === 4}}">
        <view class="bottom-action reject" bindtap="rejectFinalPrice">有异议</view>
        <view class="bottom-action confirm" bindtap="confirmFinalPrice">确认价格</view>
      </block>

      <!-- 已确认状态 -->
      <block wx:if="{{order.status === 5}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 订单完成状态 -->
      <block wx:if="{{order.status === 6}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 已取消状态 -->
      <block wx:if="{{order.status === 0}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>
    </view>
  </block>

  <!-- 订单不存在 -->
  <view class="empty-container" wx:if="{{!loading && !order}}">
    <image class="empty-icon" src="/images/icons/empty-order.png"></image>
    <view class="empty-text">订单不存在或已被删除</view>
    <navigator url="/pages/order/list" class="go-back">返回订单列表</navigator>
  </view>

  <!-- 取消订单面板 -->
  <view class="modal {{showCancelModal ? 'show' : ''}}">
    <view class="modal-mask" bindtap="hideCancel"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">取消订单</view>
        <view class="modal-close" bindtap="hideCancel">×</view>
      </view>
      <view class="modal-body">
        <view class="cancel-title">请选择取消原因</view>
        <view class="cancel-reasons">
          <view
            class="cancel-reason-item {{selectedReason === item ? 'selected' : ''}}"
            wx:for="{{cancelReasons}}"
            wx:key="*this"
            bindtap="selectReason"
            data-reason="{{item}}"
          >
            {{item}}
          </view>
        </view>
        <view class="cancel-other" wx:if="{{selectedReason === '其他原因'}}">
          <input
            class="cancel-input"
            placeholder="请输入取消原因"
            value="{{cancelReason}}"
            bindinput="inputCancelReason"
          />
        </view>
        <view class="cancel-button" bindtap="confirmCancel">确认取消</view>
      </view>
    </view>
  </view>

</view>
